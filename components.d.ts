/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
// biome-ignore lint: disable
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    CallPhone: typeof import('./src/components/CallPhone.vue')['default']
    Map: typeof import('./src/components/Map.vue')['default']
    MapDataDialog: typeof import('./src/components/mapDataDialog.vue')['default']
    MapPopup: typeof import('./src/components/mapPopup.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
  }
}
