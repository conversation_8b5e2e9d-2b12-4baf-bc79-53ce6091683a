<!-- eslint-disable vue/no-mutating-props -->
<template>
  <el-dialog v-model="props.typedialogVisible" :modal="false" :draggable="true" modal-class="supplies-dialog"
    title="防汛人员" :before-close="beforeClosed" :show-close="false" width="2000">
    <div class="leftbei"></div>
    <div class="righttbei"></div>
    <div class="bottombeibg"></div>
    <div style="margin-top: 40px; font-size: 24px">
      <div class="heaader-close" @click="close">x</div>
      <div class="data-wrap">
        <div class="data-left">
          <div class="data-title">
            <div>点位类型: 污水处理厂</div>
            <el-button size="large" type="primary">刷新</el-button>
          </div>
          <div style="padding: 14px 0;">监测数据</div>
          <div>
            &nbsp;&nbsp;&nbsp;&nbsp;水位: 4.9cm &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
            雨量: 9mm/h <br></br>
            &nbsp;&nbsp;&nbsp;&nbsp;小时雨量: 0mm &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; 流量: 0.4m³/s &nbsp;&nbsp;&nbsp;&nbsp;
            <br></br>
            &nbsp;&nbsp;&nbsp;&nbsp;流速: 0.4m/s
          </div>
          <div ref="chart1Ref" class="data-chart"></div>
        </div>
        <div class="data-right">
          <el-tabs v-model="activeName" class="demo-tabs" @tab-click="handleClick">
            <el-tab-pane label="主管信息" name="first"></el-tab-pane>
            <el-tab-pane label="责权信息" name="second">Config</el-tab-pane>
            <el-tab-pane label="属地信息" name="third"></el-tab-pane>
            <el-tab-pane label="监督信息" name="fourth"></el-tab-pane>
            <el-tab-pane label="环卫信息" name="five"></el-tab-pane>
          </el-tabs>
        </div>
      </div>
    </div>
    <!-- <div style="min-height: 900px;border: 1px solid red;"></div> -->
  </el-dialog>
</template>

<script setup>
import { ref, defineProps, watch, defineEmits, onMounted } from 'vue'
import { qyylstatGET, getwaterLevel } from '@/api/home.js'
import * as echarts from 'echarts'
// const fangxunrenyuandialogVisible = ref(false)
const emit = defineEmits(['closeMapDataDialog'])
const props = defineProps({
  typedialogVisible: {
    type: Boolean,
    default: false
  },
  typeDialogID: {
    type: String
  }
})
const activeName = ref('first')

const handleClick = (tab, event) => {
  console.log(tab, event)
}
const close = () => {
  emit('closeMapDataDialog')
}
const beforeClosed = () => {
  emit('closeMapDataDialog')
}
const wateryuliangData = ref([])
const waterLevelData = ref([])
const getwaterLevelData = async id => {
  try {
    // const yuliangRes = await qyylstatGET()
    // console.log(yuliangRes, '降雨量图表', JSON.parse(yuliangRes.data[0].value))
    // wateryuliangData.value = JSON.parse(yuliangRes.data[0].value).data
    const result = await getwaterLevel(props.typeDialogID)
    console.log('获取设备水位数据:', result)
    if (result && result.code === 200) {
      waterLevelData.value = result.rows
      console.log('获取设备水位数据成功', waterLevelData.value)
      // 数据加载完成后更新图表
      setTimeout(() => {
        initCharts()
      }, 100)
    } else {
      console.error('获取设备水位失败')
    }
  } catch (error) {
    console.error('获取设备水位失败:', error)
  }
}
let chart1Instance = null
const chart1Ref = ref(null)
// 初始化图表
const initCharts = async () => {
  // await nextTick()

  if (chart1Ref.value) {
    chart1Instance = echarts.init(chart1Ref.value)

    // 处理水位数据
    let xAxisData = []
    let seriesData = []
    let yAxisMin = 0
    let yAxisMax = 10

    if (waterLevelData.value && waterLevelData.value.length > 0) {
      // 从waterLevelData中提取时间和水位数据
      xAxisData = waterLevelData.value.map(item => {
        // 格式化时间显示，只显示时:分:秒
        const date = new Date(item.time)
        return `${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}:${String(
          date.getSeconds()
        ).padStart(2, '0')}`
      })

      seriesData = waterLevelData.value.map(item => parseFloat(item.waterlevel) || 0)

      // 动态计算Y轴范围
      if (seriesData.length > 0) {
        const minValue = Math.min(...seriesData)
        const maxValue = Math.max(...seriesData)
        const range = maxValue - minValue
        yAxisMin = Math.max(0, minValue - range * 0.1) // 最小值向下扩展10%，但不小于0
        yAxisMax = maxValue + range * 0.1 // 最大值向上扩展10%
      }
    } else {
      // 默认数据（当没有水位数据时）
      xAxisData = ['03:49:00', '05:07:00', '05:03:00', '05:10:00', '05:12:00']
      seriesData = [0, 0, 0, 0, 0]
    }

    const option1 = {
      backgroundColor: 'transparent',
      grid: {
        left: '3%',
        right: '5%',
        top: '5%',
        bottom: '0%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        data: xAxisData,
        axisLine: {
          show: false
        },
        axisTick: {
          show: false
        },
        axisLabel: {
          color: '#ffffff',
          fontSize: 34,
          margin: 8,
          // rotate: 45, // 旋转标签以避免重叠
          formatter: function (value) {
            // 如果标签太长，可以进一步简化
            return value
          }
        }
      },
      yAxis: {
        type: 'value',
        name: '水位单位:cm',
        nameTextStyle: {
          color: '#ffffff',
          fontSize: 24,
          show: false
        },
        // min: yAxisMin,
        // max: yAxisMax,
        min: 0,
        max: 10,
        axisLine: {
          show: false
        },
        axisTick: {
          show: false
        },
        axisLabel: {
          color: '#ffffff',
          fontSize: 34
        },
        splitLine: {
          show: true,
          lineStyle: {
            color: 'rgba(255, 255, 255, 0.2)',
            width: 1,
            type: 'dashed'
          }
        }
      },
      series: [
        {
          name: '水位',
          data: seriesData,
          type: 'line',
          smooth: true,
          lineStyle: {
            color: '#00ff88',
            width: 2
          },
          itemStyle: {
            color: '#00ff88',
            borderColor: '#00ff88',
            borderWidth: 2
          },
          symbol: 'circle',
          symbolSize: 4,
          areaStyle: {
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                {
                  offset: 0,
                  color: 'rgba(0, 255, 136, 0.3)'
                },
                {
                  offset: 1,
                  color: 'rgba(0, 255, 136, 0.05)'
                }
              ]
            }
          }
        }
      ],
      tooltip: {
        trigger: 'axis',
        backgroundColor: 'rgba(0, 0, 0, 0.8)',
        borderColor: '#00ff88',
        borderWidth: 0,
        textStyle: {
          color: '#ffffff',
          fontSize: 32
        },
        formatter: function (params) {
          if (params && params.length > 0) {
            const data = params[0]
            return `时间: ${data.axisValue}<br/>水位: ${data.value} cm`
          }
          return ''
        }
      }
    }
    chart1Instance.setOption(option1)
  }
}

watch(
  () => props.typedialogVisible,
  data => {
    console.log(data, 'data')
    if (data) {
      //   getwaterLevelData()
    }
  }
)
onMounted(() => {
  getwaterLevelData()
})
</script>

<style lang="scss" scoped>
.data-wrap {
  height: 700px;
  border: 1px solid red;
  display: flex;

  .data-left {
    width: 50%;
    color: #fff;
    font-size: 32px;

    .data-title {
      display: flex;
      justify-content: space-between;
    }

    .data-chart {
      margin-top: 24px;
      height: 300px;
      border: 1px solid blue;
    }
  }

  .data-right {
    width: 50%;
    border: 1px solid red;
  }
}

.leftbei {
  position: absolute;
  left: -1px;
  width: 7px;
  height: 197px;
  top: 30%;
  background: url('@/assets/images/intellisense/leftbei.png') no-repeat center/100%;
}

.righttbei {
  position: absolute;
  right: -1px;
  width: 7px;
  height: 197px;
  top: 30%;
  background: url('@/assets/images/intellisense/rightbeibg.png') no-repeat center/100%;
}

.bottombeibg {
  position: absolute;
  bottom: -1px;
  width: 460px;
  height: 6px;
  left: 30%;
  background: url('@/assets/images/intellisense/bottombeibg.png') no-repeat center/100%;

  &.bottom-left {
    left: 21%;
  }
}
</style>
<style lang="scss">
.demo-tabs {
.el-tabs__item {
  font-size: 34px;
  color: #fff;
  &.is-active {
    font-weight: 600
  }
}
.el-tabs__nav-wrap::after {
  height: 0px;
  width: 0;
}
}

.supplies-dialog {
  pointer-events: auto;

  &.fangxunrenyuan {
    .el-dialog {
      border: 2px solid rgba(64, 228, 251, 0.3);

      .el-table__row {
        .cell {
          line-height: 32px;
        }
      }
    }
  }

  .el-overlay-dialog {
    pointer-events: auto;

    .el-dialog {
      pointer-events: auto;
    }
  }

  .yuqing-table-wrap {
    .el-table__row {
      .cell {
        line-height: 32px;
      }
    }
  }
}

.supplies-dialog {
  .heaader-close {
    position: absolute;
    top: 30px;
    right: 40px;
    width: 50px;
    height: 50px;
    color: #fff;
    font-size: 48px;
    // background: url('@/assets/images/dialognewclose.png') no-repeat center/100%;
    cursor: pointer;
  }

  .new-header-title {
    height: 90px;
    display: flex;
    justify-content: space-between;
    align-items: flex-start;

    .header-name {
      position: relative;
      top: 8px;
      font-size: 28px;
      color: #fff;
    }
  }

  .el-dialog {
    background: rgba(26, 72, 123, 0.9693);
    background: url('@/assets/images/dialognew.png') no-repeat center/100%;
    // box-shadow: inset 0px 0px 57px 0px #0e4571;
    border-radius: 0px 0px 0px 0px;
    // border: 1px solid #4190d8;
    padding: 0;

    .el-dialog__header {
      // display: none;
      height: 50px;
      // background: linear-gradient(270deg, #2882bf 0%, rgba(38, 128, 190, 0.3862) 100%);
      // border-radius: 0px 0px 0px 0px;
      padding-left: 30px;

      .el-dialog__title {
        line-height: 50px;
        color: #fff;
        font-size: 24px;
      }

      .el-dialog__close {
        color: #fff;
        font-size: 30px;
      }
    }

    .el-dialog__body {
      padding-left: 50px;
      padding-right: 50px;
      padding-bottom: 80px;

      .el-form-item__label {
        color: #fff;
        font-size: 22px;
      }

      .el-select__wrapper {
        background: transparent;
        box-shadow: 0 0 0 1px #4190d8 inset;

        .el-select__selected-item {
          color: #fff;
          font-size: 18px;
        }
      }

      .el-input__wrapper {
        background: transparent;
        box-shadow: 0 0 0 1px #4190d8 inset;

        .el-input__inner {
          color: #fff;
          font-size: 18px;
        }

        .el-input__inner::-webkit-input-placeholder {
          color: #fff;
        }

        .el-input__inner::-moz-placeholder {
          color: #fff;
        }
      }

      .el-button {
        background: rgba(0, 147, 255, 0.2);
        border: 1px solid #1f8ad4;
        font-family:
          Source Han Sans,
          Source Han Sans;
        font-weight: 500;
        font-size: 18px;
        color: #ffffff;
        text-align: center;
        font-style: normal;
        text-transform: none;
      }

      .el-table__row {
        background: transparent;
        cursor: pointer;

        &:nth-child(even) {
          background: rgba(27, 114, 223, 0.2);
        }

        td {
          background: transparent;
          color: #fff;
          font-size: 22px;
          // border-bottom: 1px solid rgba(216, 216, 216, 0.2);
          border-bottom: none;
          padding: 16px 0;
        }
      }

      .el-table--enable-row-hover .el-table__body tr:hover>td.el-table__cell {
        background-color: rgba(31, 138, 212, 0.4);
      }

      .el-table__header-wrapper {
        tr {
          background: transparent;
          // background: rgba(52, 120, 187, 0.3632);
          // border-radius: 2px 2px 2px 2px;
          background: #1b72df;
          border-radius: 0px 0px 0px 0px;

          // border: 1px solid rgba(60, 139, 217, 0.4542);
          th {
            background: transparent;
            font-size: 20px;
            color: #fff;
            border-bottom: none;
          }
        }
      }

      .el-table--fit .el-table__inner-wrapper::before {
        width: 0px;
      }

      .el-pager {
        li {
          background: transparent;
          color: #d8d8d8;
          font-size: 18px;

          &.is-active {
            background: #008aff;
          }
        }
      }

      .el-pagination {
        float: right;

        button {
          background: transparent;
        }

        .btn-prev,
        .btn-next {
          color: #fff;
        }

        .el-pagination__total,
        .el-pagination__jump {
          color: #fff;
          font-size: 18px;
        }
      }
    }
  }
}
</style>
